<?php

declare(strict_types=1);

namespace App\Testing;

use App\Testing\Fixtures\AbstractMockedResponse;
use Demv\SdkFramework\Gateway\GatewayInterface;
use GuzzleHttp\Psr7\Response;
use Illuminate\Support\Facades\DB;
use Psr\Http\Message\ResponseInterface;

abstract class MockedGateway implements GatewayInterface
{
    /**
     * @return class-string<AbstractMockedResponse>[]
     */
    abstract protected function getMockedResponses(): array;

    public function request(
        string $method,
        string $uri,
        array $requestOptions = [],
        array $middlewares = [],
        bool $throwHttpError = false,
    ): ResponseInterface {
        $response = DB::select(
            'select id, body from testing_mock_db where method = ? AND url = ?',
            [
                $method,
                $uri,
            ],
        );
        $response = $response[0] ?? null;

        // TODO implement default response body option
        if ($response !== null) {
            DB::delete('delete from testing_mock_db where id= ?', [$response->id]);

            $body = $response->body;

            return new Response(status: 200, body: $body);
        }

        $mockedResponse = $this->findResponseBody($method, $uri);

        if ($mockedResponse === null) {
            throw new \Exception("No Response Found for {$method}::{$uri}");
        }

        return new Response(status: 200, body: $mockedResponse);
    }

    private function findResponseBody(string $method, string $uri): ?string
    {
        foreach ($this->getMockedResponses() as $mockedResponse) {
            if ($mockedResponse::getRequestMethod() === $method
                && $mockedResponse::isMatchingUrl($uri)) {
                return $mockedResponse::getMockedResponseBody($uri);
            }
        }

        return null;
    }

    public function getBaseUrl(): string
    {
        return '';
    }

    public function get(string $uri, array $params = [], array $middlewares = [], bool $throw = false): ResponseInterface
    {
        if ($params !== [] && preg_match('#/api/v1/\d+/broker-nrs#', $uri) === 1) {
            $queryString = http_build_query($params);
            $uri .= (str_contains($uri, '?') ? '&' : '?') . $queryString;
        }

        return $this->request('get', $uri);
    }

    public function post(string $uri, array $requestOptions = [], array $middlewares = [], bool $throw = false): ResponseInterface
    {
        return $this->request('post', $uri);
    }

    public function put(string $uri, array $requestOptions = [], array $middlewares = [], bool $throw = false): ResponseInterface
    {
        return $this->request('put', $uri);
    }
}
