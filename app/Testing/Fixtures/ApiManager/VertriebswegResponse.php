<?php

declare(strict_types=1);

namespace App\Testing\Fixtures\ApiManager;

use App\Testing\Fixtures\AbstractMockedResponse;

class VertriebswegResponse
{
    public static function getRequestMethod(): string
    {
        return AbstractMockedResponse::HTTP_GET;
    }

    public static function isMatchingUrl(string $url): bool
    {
        $path = parse_url($url, PHP_URL_PATH);
        if (!is_string($path)) {
            return false;
        }
        return preg_match('#/api/v1/\d+/broker-nrs$#', $path) === 1;
    }

    public static function getMockedResponseBody(string $url): string
    {
        preg_match('#/api/v1/(?\'user_id\'\d+)/broker-nrs#', $url, $matches);
        $userId = $matches['user_id'] ?? '';

        // Die HDI Sachversicherungen AG (id 156) ist die einzige Gesellschaft, wo der Makler eine Vermittlernummer hat
        if (preg_match('#insurance_company_ids(\[0]|%5B0%5D)=156#', $url) === 1) {
            return <<<JSON
            {
              "data": [
                        {
                          "url": "http://api.professionalworks.demv.internal/api/v1/1/broker-nrs/156",
                          "id": 4339,
                          "broker_nr": "*********",
                          "type": {
                            "id": 1,
                            "name": "Antragsnummer"
                          },
                          "status": {
                            "name": "Aktiv",
                            "description": ""
                          },
                          "is_validated": false,
                          "is_personalized": false,
                          "is_active": true,
                          "is_imported": false,
                          "is_pool": false,
                          "is_linked": false,
                          "insurance_company_id": 156,
                          "insurance_company_url": "http://api.professionalworks.demv.internal/api/v1/insurance-companies/156",
                          "pw_user_id": 1,
                          "pw_user_url": "http://api.professionalworks.demv.internal/api/v1/1/me",
                          "create_type": {
                            "id": 0,
                            "name": "NOT_DEFINED"
                          },
                          "metadata": {
                            "is_csv_excluded": false,
                            "created_at": "2025-03-28 15:35:34",
                            "updated_at": "2025-03-28 15:35:34",
                            "updated_by_id": 1,
                            "updated_by_url": "http://api.professionalworks.demv.internal/api/v1/1/me"
                          }
                        }

                ]
            }
            JSON;
        }

        return <<<JSON
        {
          "data": []
        }
        JSON;
    }
}
